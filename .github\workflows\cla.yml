
name: "cla-bot"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]

jobs:
  cla-check:
    if: github.repository_owner == 'mlcommons'
    runs-on: ubuntu-latest
    steps:
      - name: "MLCommons CLA bot check"
        if: (github.event.comment.body == 'recheck') || github.event_name == 'pull_request_target'
        # Alpha Release
        uses: mlcommons/cla-bot@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # the below token should have repo scope and must be manually added by you in the repository's secret
          PERSONAL_ACCESS_TOKEN : ${{ secrets.MLCOMMONS_BOT_CLA_TOKEN }}
        with:
          path-to-signatures: 'cla-bot/v1/cla.json'
          # branch should not be protected
          branch: 'main'
          allowlist: user1,bot*,github-actions,github-actions[bot]
          remote-organization-name: mlcommons
          remote-repository-name: systems
          
         #below are the optional inputs - If the optional inputs are not given, then default values will be taken
          #remote-organization-name: enter the remote organization name where the signatures should be stored (<PERSON><PERSON><PERSON> is storing the signatures in the same repository)
          #remote-repository-name:  enter the  remote repository name where the signatures should be stored (Default is storing the signatures in the same repository)
          #create-file-commit-message: 'For example: Creating file for storing CLA Signatures'
          #signed-commit-message: 'For example: $contributorName has signed the CLA in #$pullRequestNo'
          #custom-notsigned-prcomment: 'pull request comment with Introductory message to ask new contributors to sign'
          #custom-pr-sign-comment: 'The signature to be committed in order to sign the CLA'
          #custom-allsigned-prcomment: 'pull request comment when all contributors has signed, defaults to **CLA Assistant Lite bot** All Contributors have signed the CLA.'
