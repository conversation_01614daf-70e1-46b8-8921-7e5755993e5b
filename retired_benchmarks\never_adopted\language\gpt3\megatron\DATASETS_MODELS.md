# Datasets

This is a comprehensive list of public datasets and models used by this repository.

| Name (Link/Source) | Framework | Use Case |
|--------------------| --------- | -------- |
| [cnn_dailymail (Hugging Face)](https://huggingface.co/datasets/cnn_dailymail) | PyTorch | Text Summarization |
| [gpt-3-175b (Megatron implementation)](https://github.com/mlcommons/training/tree/master/large_language_model/megatron-lm) | PyTorch | Text Summarization |
