# **Release Notes**  

🚀 **mlc-scripts 1.0.0** was released on **February 10, 2025**, introducing full support for **MLPerf Inference v5.0** using the first stable release of **MLCFlow 1.0.1**.  

🔹 All previous **CM scripts** used in MLPerf have been successfully **ported to the MLC interface**, ensuring seamless integration. Additionally, all **GitHub Actions** are now passing, confirming a stable and reliable implementation.  

## **Key Updates in MLCFlow**  

✅ **Simplified Interface**  
- A redesigned approach using **Actions and Targets**, making the CLI more intuitive for users.  

✅ **Unified Automation Model**  
- Consolidated into a **single automation entity**: **Script**, which is seamlessly extended by **<PERSON>ache, Docker, and Tests**.  

✅ **Improved Docker Integration**  
- A **cleaner, more efficient Docker extension**, streamlining containerized execution.  

✅ **Enhanced Script Management**  
- Tighter integration between the interface and script automation, making script **creation and management easier than ever**.  
