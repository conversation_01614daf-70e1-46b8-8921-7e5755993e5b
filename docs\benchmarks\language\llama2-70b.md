---
hide:
  - toc
---

# Text Summarization using LLAMA2-70b

=== "MLCommons-Python"
    ## MLPerf Reference Implementation in Python
    
{{ mlperf_inference_implementation_readme (4, "llama2-70b-99", "reference") }}

{{ mlperf_inference_implementation_readme (4, "llama2-70b-99.9", "reference") }}

=== "Nvidia"
    ## Nvidia MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "llama2-70b-99", "nvidia") }}

{{ mlperf_inference_implementation_readme (4, "llama2-70b-99.9", "nvidia") }}

=== "Neural Magic"
    ## Neural Magic MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "llama2-70b-99", "neuralmagic") }}

{{ mlperf_inference_implementation_readme (4, "llama2-70b-99.9", "neuralmagic") }}

=== "AMD"
    ## AMD MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "llama2-70b-99", "amd") }}

{{ mlperf_inference_implementation_readme (4, "llama2-70b-99.9", "amd") }}
