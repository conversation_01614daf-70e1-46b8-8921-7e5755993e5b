generated_doxygen_out_dir =
    get_path_info(".", "gen_dir") + "/.."

loadgen_doxygen_sources = [
  "doxygen.cfg",
  "doxygen_footer.html",
  "doxygen_header.html",
  "doxygen_layout.xml",
  "doxygen_stylesheet.css",
  "loadgen-integration_diagram.dia",
  "mlperf_icon.png",
  "mlperf_logo_horizontal_color.svg",
  "README.md"
]

source_set("loadgen_doxygen_sources") {
  sources = loadgen_doxygen_sources
}

source_set("doxygen_html_generator_script") {
  sources = [ "doxygen_html_generator.py" ]
}

action("generate_doxygen_html") {
  script = "doxygen_html_generator.py"
  args = [ rebase_path(generated_doxygen_out_dir, root_build_dir),
           rebase_path("../..") ]
  outputs = [ generated_doxygen_out_dir ]
  deps = [ ":loadgen_doxygen_sources",
           ":doxygen_html_generator_script",
           "../..:mlperf_loadgen_sources_no_gen",
           "../..:docs" ]
}
