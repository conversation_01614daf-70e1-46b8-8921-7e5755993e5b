#!/usr/bin/env bash
# wkong: manually set the user info in env first

set -ex

if [ -z "$@" ]; then
  COMMAND=(bash)
else
  COMMAND=("$@")
fi

apt-get update && apt-get install -y sudo

getent group "${CI_BUILD_GID}" || addgroup --gid "${CI_BUILD_GID}" "${CI_BUILD_GROUP}"
getent passwd "${CI_BUILD_UID}" || adduser --gid "${CI_BUILD_GID}" --uid "${CI_BUILD_UID}" --gecos "${CI_BUILD_USER} (generated by with_the_same_user script)" --disabled-password --quiet "${CI_BUILD_USER}"

usermod -a -G dip "${CI_BUILD_USER}"
usermod -a -G sudo "${CI_BUILD_USER}"
usermod -a -G root "${CI_BUILD_USER}"

echo '%sudo ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers
mkdir -p /home/"${CI_BUILD_USER}"
touch /home/"${CI_BUILD_USER}"/.bashrc
echo 'export PATH="$PATH:/opt/miniconda3/bin"' >> /home/"${CI_BUILD_USER}"/.bashrc

sudo -H -u "#${CI_BUILD_UID}" --preserve-env \
  PATH="${PATH}" \
  LD_LIBRARY_PATH="${LD_LIBRARY_PATH}" \
  PYTHONPATH="${PYTHONPATH}" \
  bash -c "conda init bash"

echo 'conda activate llama2-70b' >> /home/"${CI_BUILD_USER}"/.bashrc


sudo -H -u "#${CI_BUILD_UID}" --preserve-env \
  PATH="${PATH}" \
  LD_LIBRARY_PATH="${LD_LIBRARY_PATH}" \
  PYTHONPATH="${PYTHONPATH}" \
  ${COMMAND[@]}
