# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Test for MLPerf inference loadgen using MLC script automation

on:
  pull_request:
    branches: [ "master", "dev" ]
    paths:
      - loadgen/**
      - .github/workflows/test-loadgen.yml
      - '!**.md'

env:
  PR_HEAD_REF: ${{ github.event.pull_request.head.ref }}

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python3 -m pip install mlc-scripts
    - name: Test Loadgen
      run: |
        mlcr get,mlperf,inference,loadgen --quiet --version=custom --adr.inference-src.tags=_repo.${{ github.event.pull_request.head.repo.html_url }},_branch.$PR_HEAD_REF --adr.loadgen.tags=_no-compilation-warnings
