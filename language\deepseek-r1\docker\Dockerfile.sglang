# SGLang Backend Dockerfile
FROM nvidia/cuda:12.6.0-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PIP_BREAK_SYSTEM_PACKAGES=1
ENV MLPERF_BACKEND=sglang

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    software-properties-common \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3.10-distutils \
    python3-pip \
    git \
    git-lfs \
    curl \
    wget \
    ca-certificates \
    cmake \
    build-essential \
    ninja-build \
    pybind11-dev \
    pkg-config \
    sudo \
    libnuma-dev \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.10 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1 && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1

# Install pip for Python 3.10
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10

# Install UV package manager system-wide
RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
    mv /root/.local/bin/uv /usr/local/bin/uv && \
    mv /root/.local/bin/uvx /usr/local/bin/uvx && \
    chmod 755 /usr/local/bin/uv /usr/local/bin/uvx && \
    uv --version

# Install base Python packages
RUN pip3 install --no-cache-dir \
    numpy \
    setuptools \
    wheel \
    "pybind11>=2.11.1"

# Set CUDA environment for proper compilation
ENV CUDA_HOME=/usr/local/cuda

# Set cache directory environment variables
# These will be overridden at runtime if needed
ENV HF_HOME=/raid/data/\$USER/.cache/huggingface
ENV HF_HUB_CACHE=/raid/data/\$USER/.cache/huggingface
ENV HUGGINGFACE_HUB_CACHE=/raid/data/\$USER/.cache/huggingface

# Create necessary directories
RUN mkdir -p /work /work/logs

# Set working directory to /work for mounted workspace
WORKDIR /work

# Add /work to PATH so setup.sh can be run without ./
ENV PATH="/work:${PATH}"

# Set default command
CMD ["/bin/bash"]