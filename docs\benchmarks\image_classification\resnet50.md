---
hide:
  - toc
---


# Image Classification using ResNet50 


=== "MLCommons-Python"
    ## MLPerf Reference Implementation in Python
    
{{ mlperf_inference_implementation_readme (4, "resnet50", "reference") }}

=== "Nvidia"
    ## Nvidia MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "resnet50", "nvidia") }}

=== "Intel"
    ## Intel MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "resnet50", "intel") }}

=== "Qualcomm"
    ## Qualcomm AI100 MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "resnet50", "qualcomm") }}

=== "MLCommons-C++"
    ## MLPerf Modular Implementation in C++
    
{{ mlperf_inference_implementation_readme (4, "resnet50", "cpp") }}
