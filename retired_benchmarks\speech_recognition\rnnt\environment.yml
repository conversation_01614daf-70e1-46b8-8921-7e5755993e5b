name: mlperf-rnnt
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - absl-py=0.9.0=py36_0
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2020.4.5.1=hecc5488_0
  - certifi=2020.4.5.1=py36h9f0ad1d_0
  - cffi=1.14.0=py36h2e261b9_0
  - cmake=3.14.0=h52cb24c_0
  - cudatoolkit=10.1.243=h6bb024c_0
  - cudatoolkit-dev=10.1.243=h516909a_3
  - expat=2.2.6=he6710b0_0
  - freetype=2.9.1=h8a8886c_1
  - gdb=8.3.1=py36h497da48_1
  - intel-openmp=2020.0=166
  - jpeg=9b=h024ee3a_2
  - krb5=1.17.1=h173b8e3_0
  - lame=3.100=h14c3975_1001
  - ld_impl_linux-64=2.33.1=h53a641e_7
  - libcurl=7.69.1=h20c2e04_0
  - libedit=3.1.20181209=hc058e9b_0
  - libffi=3.2.1=hd88cf55_4
  - libgcc-ng=9.1.0=hdf63c60_0
  - libgfortran-ng=7.3.0=hdf63c60_0
  - libpng=1.6.37=hbc83047_0
  - libssh2=1.9.0=h1ba5d50_1
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - libtiff=4.1.0=h2733197_0
  - mad=0.15.1b=he1b5a44_0
  - mkl=2020.0=166
  - mkl-include=2020.0=166
  - mkl-service=2.3.0=py36he904b0f_0
  - mkl_fft=1.0.15=py36ha843d7b_0
  - mkl_random=1.1.0=py36hd6b4f25_0
  - ncurses=6.1=hf484d3e_1002
  - ninja=1.9.0=py36hfd86e86_0
  - numpy=1.18.1=py36h4f9e942_0
  - numpy-base=1.18.1=py36hde5b4d6_1
  - olefile=0.46=py_0
  - openssl=1.1.1g=h516909a_0
  - pillow=7.0.0=py36hb39fc2d_0
  - pip=20.0.2=py36_1
  - pycparser=2.20=py_0
  - python=3.6.10=h0371630_0
  - python_abi=3.6=1_cp36m
  - pytorch=1.5.0=py3.6_cuda10.1.243_cudnn7.6.3_0
  - pyyaml=5.3.1=py36h7b6447c_0
  - readline=7.0=hf8c457e_1001
  - rhash=1.3.8=h1ba5d50_0
  - setuptools=46.1.3=py36_0
  - six=1.14.0=py36_0
  - sqlite=3.31.1=h7b6447c_0
  - tk=8.6.8=hbc83047_0
  - torchvision=0.6.0=py36_cu101
  - wheel=0.34.2=py36_0
  - xz=5.2.4=h14c3975_4
  - yaml=0.1.7=had09818_2
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.3.7=h0b5b093_0
  - pip:
    - ascii-graph==1.5.1
    - attrs==19.3.0
    - audioread==2.1.8
    - autopep8==1.5.1
    - backcall==0.1.0
    - chardet==3.0.4
    - coverage==5.0.4
    - decorator==4.4.2
    - entrypoints==0.3
    - flake8==3.7.9
    - grpcio==1.28.1
    - idna==2.9
    - importlib-metadata==1.6.0
    - inflect==4.1.0
    - ipdb==0.13.2
    - ipython==7.13.0
    - ipython-genutils==0.2.0
    - jedi==0.16.0
    - joblib==0.14.1
    - librosa==0.7.2
    - llvmlite==0.31.0
    - markdown==3.2.1
    - mccabe==0.6.1
    - more-itertools==8.2.0
    - numba==0.48.0
    - onnx==1.6.0
    - onnxruntime==1.2.0
    - packaging==20.3
    - pandas==0.24.2
    - parso==0.6.2
    - pexpect==4.8.0
    - pickleshare==0.7.5
    - pluggy==0.13.1
    - prompt-toolkit==3.0.5
    - protobuf==3.11.3
    - ptyprocess==0.6.0
    - py==1.8.1
    - pycodestyle==2.5.0
    - pyflakes==2.1.1
    - pygments==2.6.1
    - pyparsing==2.4.7
    - pytest==5.4.2
    - python-dateutil==2.8.1
    - pytz==2019.3
    - requests==2.23.0
    - resampy==0.2.2
    - scikit-learn==0.22.2.post1
    - scipy==1.4.1
    - soundfile==0.10.3.post1
    - sox==1.3.7
    - tensorboard==2.0.0
    - toml==0.10.0
    - tqdm==4.31.1
    - traitlets==4.3.3
    - typing-extensions==*******
    - unidecode==1.1.1
    - urllib3==1.25.8
    - wcwidth==0.1.9
    - werkzeug==1.0.1
    - wrapt==1.10.11
    - zipp==3.1.0
prefix: /cb/home/<USER>/ws/miniconda3/envs/mlperf-rnnt

