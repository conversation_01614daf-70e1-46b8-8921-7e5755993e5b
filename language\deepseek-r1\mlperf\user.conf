# The format of this config file is 'key = value'.
# The key has the format 'model.scenario.key'. Value is mostly int64_t.
# Model maybe '*' as wildcard. In that case the value applies to all models.
# All times are in milli seconds

*.Offline.target_qps = 1.0
*.Offline.min_duration = 600000
*.Offline.min_query_count = 4388

*.Server.target_qps = 1.0
*.Server.min_duration = 600000
*.Server.min_query_count = 4388