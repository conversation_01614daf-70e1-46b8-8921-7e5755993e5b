static_library("mlperf_loadgen_tests_loadgen_test_main") {
  sources = [ "loadgen_test.h", "loadgen_test_main.cc" ]
  configs += [ "//build/config/compiler:exceptions" ]
}

executable("mlperf_loadgen_perftests") {
  sources = [ "perftests_null_sut.cc" ]
  deps = [ "..:mlperf_loadgen" ]
}

executable("mlperf_loadgen_tests_basic") {
  sources = [ "basic.cc" ]
  deps = [ "..:mlperf_loadgen",
           ":mlperf_loadgen_tests_loadgen_test_main"  ]
  configs += [ "//build/config/compiler:exceptions" ]
}

source_set("mlperf_loadgen_perftests_py") {
  sources = [ "perftests_null_sut.py" ]
  deps = [ "../..:loadgen_pymodule_wheel_lib" ]
}

source_set("docs") {
  sources = [ "README.md" ]
}
