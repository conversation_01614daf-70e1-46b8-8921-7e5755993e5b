{"attention": "normed_bahdanau", "attention_architecture": "gnmt_v2", "batch_size": 128, "colocate_gradients_with_ops": true, "dropout": 0.2, "encoder_type": "gnmt", "eos": "</s>", "forget_bias": 1.0, "init_weight": 0.1, "learning_rate": 1.0, "max_gradient_norm": 5.0, "metrics": ["bleu"], "num_buckets": 5, "num_encoder_layers": 4, "num_decoder_layers": 4, "num_train_steps": 340000, "decay_scheme": "luong10", "num_units": 1024, "optimizer": "sgd", "residual": true, "share_vocab": false, "subword_option": "bpe", "sos": "<s>", "src_max_len": 50, "src_max_len_infer": null, "steps_per_external_eval": null, "steps_per_stats": 100, "tgt_max_len": 50, "tgt_max_len_infer": null, "time_major": true, "unit_type": "lstm", "infer_mode": "beam_search", "beam_width": 10, "length_penalty_weight": 1.0}