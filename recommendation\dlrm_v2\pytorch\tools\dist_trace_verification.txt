In order to verify that the random number generator on your system
is compatible with the one used in the reference implementation:

1. Please generate a sample trace with the following command
./run_local.sh pytorch dlrm terabyte cpu --count-samples=100 --scenario Offline --max-ind-range=40000000 --samples-to-aggregate-quantile-file=./tools/dist_quantile.txt --max-batchsize=128

2. Verify the trace (in file dlrm_trace_of_aggregated_samples.txt)
0, 200, 200
200, 400, 200
400, 600, 200
600, 800, 200
800, 1300, 500
1300, 2000, 700
2000, 2200, 200
2200, 2300, 100
2300, 2400, 100
2400, 2900, 500
2900, 3200, 300
3200, 3400, 200
3400, 3500, 100
3500, 3800, 300
3800, 3900, 100
3900, 4200, 300
4200, 4900, 700
4900, 5200, 300
5200, 5400, 200
5400, 5500, 100
5500, 5900, 400
5900, 6100, 200
6100, 6600, 500
6600, 6800, 200
6800, 7000, 200
7000, 7200, 200
7200, 7400, 200
7400, 7700, 300
7700, 8100, 400
8100, 8300, 200
8300, 8500, 200
8500, 8700, 200
8700, 8900, 200
8900, 9000, 100
9000, 9200, 200
9200, 9300, 100
9300, 9500, 200
9500, 9700, 200
9700, 9900, 200
9900, 10100, 200
10100, 10300, 200
10300, 10400, 100
10400, 10600, 200
10600, 10800, 200
10800, 11000, 200
11000, 11200, 200
11200, 11800, 600
11800, 12000, 200
12000, 12300, 300
12300, 12500, 200
12500, 12700, 200
12700, 12900, 200
12900, 13100, 200
13100, 13800, 700
13800, 14200, 400
14200, 14400, 200
14400, 14700, 300
14700, 14900, 200
14900, 15100, 200
15100, 15300, 200
15300, 15400, 100
15400, 15600, 200
15600, 15800, 200
15800, 16400, 600
16400, 16900, 500
16900, 17100, 200
17100, 17700, 600
17700, 18200, 500
18200, 18300, 100
18300, 19000, 700
19000, 19200, 200
19200, 19400, 200
19400, 19800, 400
19800, 20300, 500
20300, 20500, 200
20500, 20700, 200
20700, 20900, 200
20900, 21100, 200
21100, 21300, 200
21300, 21500, 200
21500, 21700, 200
21700, 21900, 200
21900, 22100, 200
22100, 22800, 700
22800, 23000, 200
23000, 23200, 200
23200, 23500, 300
23500, 23700, 200
23700, 24400, 700
24400, 24600, 200
24600, 24800, 200
24800, 25000, 200
25000, 25500, 500
25500, 25800, 300
25800, 26000, 200
26000, 26200, 200
26200, 26400, 200
26400, 26500, 100
26500, 26800, 300
26800, 27000, 200
