# The format of this config file is 'key = value'.
# The key has the format 'model.scenario.key'. Value is mostly int64_t.
# Model maybe '*' as wildcard. In that case the value applies to all models.
# All times are in milli seconds
*.MultiStream.mode = 2
*.MultiStream.performance_issue_unique = 0
*.MultiStream.performance_issue_same = 1
*.MultiStream.performance_issue_same_index = 3
*.Offline.mode = 2
*.Offline.performance_issue_unique = 0
*.Offline.performance_issue_same = 1
*.Offline.performance_issue_same_index = 3
*.SingleStream.mode = 2
*.SingleStream.performance_issue_unique = 0
*.SingleStream.performance_issue_same = 1
*.SingleStream.performance_issue_same_index = 3
*.Server.mode = 2
*.Server.performance_issue_unique = 0
*.Server.performance_issue_same = 1
*.Server.performance_issue_same_index = 3
stable-diffusion-xl.Offline.min_query_count = 500
# You can optionally set the target qps to match the expected query count 
# in the min duration with the min_query_count. But take into account you
# system expected qps, the min duration for this test is 10 minutes
# stable-diffusion-xl.Offline.target_qps = 0.75
