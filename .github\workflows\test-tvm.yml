# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Test for MLPerf inference resnet50 TVM backend using MLC script automation

on:
  pull_request:
    branches: [ "master", "dev" ]
    paths:
      - vision/classification_and_detection/backend_tvm.py
      - vision/classification_and_detection/main.py
      - .github/workflows/test-tvm.yml
      - '!**.md'

env:
  PR_HEAD_REF: ${{ github.event.pull_request.head.ref }}

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ "3.10" ]
        backend: [ "tvm-onnx" ]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python3 -m pip install mlc-scripts
    - name: Test Resnet50 TVM backend
      run: |
        mlcr run,mlperf,inference,generate-run-cmds --quiet --submitter="MLCommons" --hw_name=default --model=resnet50 --implementation=reference --backend=${{ matrix.backend }} --device=cpu --scenario=Offline --test_query_count=5 --adr.compiler.tags=gcc  --adr.inference-src.version=custom --adr.inference-src.tags=_repo.${{ github.event.pull_request.head.repo.html_url }},_branch.$PR_HEAD_REF
