:::<PERSON><PERSON><PERSON><PERSON> {"key": "error_invalid_config", "value": "Multiple conf files are used. This is not valid for official submission.", "time_ms": 18446744073709.254158, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": true, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 538, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_version", "value": "4.1 @ f74d16f541", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 53, "pid": 3279, "tid": 3279}}
:::<PERSON><PERSON><PERSON><PERSON> {"key": "loadgen_build_date_local", "value": "2024-10-28T22:57:16.905751", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 55, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_build_date_utc", "value": "2024-10-28T22:57:16.905756", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 56, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_git_commit_date", "value": "2024-10-22T15:38:02-05:00", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 57, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_git_log_message", "value": "f74d16f54131d9080b9e45f234cc23e0ebaaf20c Apply min_new_tokens=2 to mixtral-8x7b, address #1777 (#1884)\necb880167756cb4b36ad70766b8d3254bfb06d26 [Postmortem 4.1] Make mlperf.conf static in loadgen, enable automatic Pypi release (#1882)\nf5c8f1758374aeaba26b2e84d31690111cfdf054 Fix bug: Loadgen ignoring token latency targets in user conf (#1874)\n976bb1ad9c7946be79507f3ff67955c27426af52 Set correct remote repo (#1871)\n41fa8aadd1ba0ecc97f6a519d8b42b04278e5f24 Add format files github action (#1682)\n518b454fd8647bfbd23a074e875e87353f33393e Tflite tpu (#1449)\ne0fdec1c7a75c98cfc194f13d62ac4388d419c8a Fix link in GettingStarted.ipynb (#1512)\n92bd8198d15411d7fb7d7c27f8904bc5a0bcfe7a Fix warning in the submission checker (#1808)\n224cfbf5c0e82cae6d48620025b7e1258ae3666a Fix typo in reference datatype (#1851)\n3ef1249b7f50a250c02c568342e0aea6638fc5a7 Fix docs (#1853)\na0874c100c54cbc54fb743ac8bf9fb5fadc64135 Update build_wheels.yml (#1758)\n6eff09986e337ccf03f675c9f244d8ee93644e16 Extend the final report generation script to output a json file of results (#1825)\n54f3f93a73cc8ca5e3319ad87fb325e510574f56 Add binding for server_num_issue_query_threads parameter (#1862)\nc4d0b3ea98e6fe7252e50cb573f0d523da7979df Update docs: SCC24,  fix broken redirect  (#1843)\n7d2f0c41e5cd79c9178702867392e38f57953338 Update DLRM readme (#1811)\ncf5fddc5d0746bf3820eb0ab7294bbf709d788ab Enable systems to be marked as power only (#1850)", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 58, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_git_status_message", "value": "", "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 60, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loadgen_file_sha1", "value": {"/.clang-format":"012aad77e5206c89d50718c46c119d1f3cb056b2","/CMakeLists.txt":"b73434348f7860471606aaa395b570e81113cb6d","/MANIFEST.in":"8d3c4ac6c325b7b9a0fd4cf4a4108cbeff8d5025","/README.md":"20a55bb946c2c0bbb564ced2af1e48efd096b3a8","/README_BUILD.md":"5f6c6a784e9cd6995db47f9b9f70b1769909c9d8","/README_FAQ.md":"01f9ae9887f50bc030dc6107e740f40c43ca388f","/VERSION.txt":"cb67dcc41adcbb7849a0a808a501ee9ccd951d92","/__init__.py":"da39a3ee5e6b4b0d3255bfef95601890afd80709","/bindings/c_api.cc":"32181da9e161c285f8fe46ddaa49e6cba2f9f918","/bindings/c_api.h":"91f58bd79b83b278f3240174a9af747fc38aff74","/bindings/python_api.cc":"9f538d2a5390c77ae0bc3f8a351bcdb2587bc66c","/diagram_network_submission.png":"53dba8ad4272190ceb6335c12fd25e53dc02a8cb","/diagram_submission.png":"84c2f79309b237cef652aef6a187ba8e875a3952","/early_stopping.cc":"0cd7b546a389deac73f7955cd39255ed76557d62","/early_stopping.h":"158fcae6a5f47e82150d6416fa1f7bcef37e77fe","/issue_query_controller.cc":"126e952d00f4ea9efd12405fb209aa3ed585e4b2","/issue_query_controller.h":"923d9d5cdf598e3ec33d7a1110a31f7e11527ec7","/loadgen.cc":"6650091ba7a918f343b06eb7a5aa540eae87275f","/loadgen.h":"e00fdc6dbc85a8c9a8485dbcbfe2944f81251c4e","/loadgen_integration_diagram.svg":"47f748307536f80cfc606947b440dd732afc2637","/logging.cc":"197efc96d178e5d33a750d07fa7b2966417506ea","/logging.h":"ddb961df7bcc145bcd7cce8c21f7cf075350dcbe","/mlperf.conf":"0a4daef277bb3151139980e484dd5e644bf36e18","/pyproject.toml":"712fab87b72ba67ef2a068d0f9f47da65130342f","/query_dispatch_library.h":"13ad6d842200cb161d6927eb74a3fafd79c46c75","/query_sample.h":"e9187c8612bbdc972305b789feb6e15c26e96cfe","/query_sample_library.h":"8323a2225be1dff31f08ecc86b76eb3de06568bc","/requirements.txt":"a5ff7e77caa6e9e22ada90f0de0c865c987bf167","/results.cc":"34e2d2a44324cb07c884f92146ecbb8ef9d704e2","/results.h":"fce22d5a588d91fd968a6b25c27896dba87bc276","/setup.py":"a722046e05858c6d9f38f0e2b3fe425334beef28","/system_under_test.h":"18d4809589dae33317d88d9beeb5491a6e1ccdec","/test_settings.h":"476ecd4032f3bafe6f201df25d68aca4e177f659","/test_settings_internal.cc":"ce4322c849d24ffafc28a37b5e528a4cb4df227d","/test_settings_internal.h":"f1d5335b53ca610c30e0edc5d07999a27b5b4b9a","/utils.cc":"3df8fdabf6eaea4697cf25d1dcb89cae88e36efd","/utils.h":"40775e32d619ea6356826ae5ea4174c7911f6894","/version.cc":"cbec2a5f98f9786c8c3d8b06b3d12df0b6550fa0","/version.h":"9d574baa64424e9c708fcfedd3dbb0b518a65fcc","/version_generator.py":"eea9b9cb1a06cd1abe1bbdaee82f9af31527fedb"}, "time_ms": 0.002363, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "version.cc", "line_no": 67, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "test_datetime", "value": "2024-10-29T02:08:01Z", "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1198, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "sut_name", "value": "PySUT", "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1199, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "get_sut_name_duration_ns", "value": 182, "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1200, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "qsl_name", "value": "PyQSL", "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1201, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "qsl_reported_total_count", "value": 198, "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1202, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "qsl_reported_performance_count", "value": 5000, "time_ms": 0.002578, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 1203, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_scenario", "value": "SingleStream", "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 271, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_test_mode", "value": "PerformanceOnly", "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 272, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_single_stream_expected_latency_ns", "value": 1e+07, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 277, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_single_stream_target_latency_percentile", "value": 0.9, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 279, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_min_duration_ms", "value": 600000, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 315, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_max_duration_ms", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 316, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_min_query_count", "value": 100, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 317, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_max_query_count", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 318, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_qsl_rng_seed", "value": 3066443479025735752, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 319, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_sample_index_rng_seed", "value": 10688027786191513374, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 320, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_schedule_rng_seed", "value": 14962580496156340209, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 322, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_accuracy_log_rng_seed", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 323, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_accuracy_log_probability", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 325, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_accuracy_log_sampling_target", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 327, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_print_timestamps", "value": false, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 329, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_performance_issue_unique", "value": false, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 330, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_performance_issue_same", "value": false, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 332, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_performance_issue_same_index", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 334, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_performance_sample_count_override", "value": 0, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 336, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "requested_sample_concatenate_permutation", "value": false, "time_ms": 0.005484, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 338, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_scenario", "value": "SingleStream", "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 417, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_test_mode", "value": "PerformanceOnly", "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 418, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_samples_per_query", "value": 1, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 420, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_target_qps", "value": 100, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 421, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_target_latency_ns", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 422, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_target_latency_percentile", "value": 0.9, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 423, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_max_async_queries", "value": 1, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 425, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_target_duration_ms", "value": 600000, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 426, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_min_duration_ms", "value": 600000, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 428, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_max_duration_ms", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 429, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_min_query_count", "value": 100, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 430, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_max_query_count", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 431, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_min_sample_count", "value": 100, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 432, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_qsl_rng_seed", "value": 3066443479025735752, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 433, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_sample_index_rng_seed", "value": 10688027786191513374, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 434, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_schedule_rng_seed", "value": 14962580496156340209, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 436, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_accuracy_log_rng_seed", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 437, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_accuracy_log_probability", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 439, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_accuracy_log_sampling_target", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 441, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_print_timestamps", "value": false, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 443, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_performance_issue_unique", "value": false, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 444, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_performance_issue_same", "value": false, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 446, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_performance_issue_same_index", "value": 0, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 448, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_performance_sample_count", "value": 5000, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 450, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "effective_sample_concatenate_permutation", "value": false, "time_ms": 0.005668, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "test_settings_internal.cc", "line_no": 452, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "generic_message", "value": "Starting performance mode", "time_ms": 0.007752, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 841, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "loaded_qsl_set", "value": [166,119,189,146,100,142,64,87,84,70,135,40,195,77,168,185,85,163,193,29,190,30,53,50,99,164,157,167,156,165,49,27,187,79,125,170,123,182,88,9,101,6,128,62,151,124,92,38,42,56,66,90,51,181,48,12,148,35,178,15,82,106,67,176,143,153,147,97,102,61,83,109,158,118,52,107,131,114,113,69,0,39,55,127,173,155,160,75,162,5,21,24,121,186,18,59,145,7,25,26,74,14,91,2,76,141,86,63,196,134,110,137,20,180,23,34,105,188,117,68,133,108,129,154,194,116,44,197,57,126,41,80,58,111,43,32,78,10,98,28,89,152,22,179,47,73,159,149,130,144,11,60,17,3,112,115,103,174,171,36,140,37,72,96,31,104,138,8,120,81,19,175,13,184,16,71,172,65,136,169,93,1,132,191,45,192,94,54,95,46,183,177,122,139,161,33,150,4], "time_ms": 0.015220, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 613, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "generated_query_count", "value": 120001, "time_ms": 25.346542, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 428, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "generated_samples_per_query", "value": 1, "time_ms": 25.346542, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 429, "pid": 3279, "tid": 3279}}
:::MLLOG {"key": "generated_query_duration", "value": 1200010000000, "time_ms": 25.346542, "namespace": "mlperf::logging", "event_type": "POINT_IN_TIME", "metadata": {"is_error": false, "is_warning": false, "file": "loadgen.cc", "line_no": 430, "pid": 3279, "tid": 3279}}
