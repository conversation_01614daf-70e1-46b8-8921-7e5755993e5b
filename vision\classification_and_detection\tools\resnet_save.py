# Copyright 2017 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Contains utility and supporting functions for ResNet.

  This module contains ResNet code which does not directly build layers. This
includes dataset management, hyperparameter and optimizer code, and argument
parsing. Code for defining the ResNet layers can be found in resnet_model.py.
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import functools
import math
import multiprocessing
import os

# pylint: disable=g-bad-import-order
from absl import flags
import tensorflow as tf
from tensorflow.contrib.data.python.ops import threadpool

from official.resnet import resnet_model
from official.utils.flags import core as flags_core
from official.utils.export import export
from official.utils.logs import hooks_helper
from official.utils.logs import logger
from official.resnet import imagenet_preprocessing
from official.utils.misc import distribution_utils
from official.utils.misc import model_helpers


def image_bytes_serving_input_fn(image_shape, dtype=tf.float32):
    """Serving input fn for raw jpeg images."""

    def _preprocess_image(image_bytes):
        """Preprocess a single raw image."""
        # Bounding box around the whole image.
        bbox = tf.constant([0.0, 0.0, 1.0, 1.0], dtype=dtype, shape=[1, 1, 4])
        height, width, num_channels = image_shape
        image = imagenet_preprocessing.preprocess_image(
            image_bytes, bbox, height, width, num_channels, is_training=False
        )
        return image

    image_bytes_list = tf.placeholder(
        shape=[None], dtype=tf.string, name="input_tensor"
    )
    images = tf.map_fn(
        _preprocess_image, image_bytes_list, back_prop=False, dtype=dtype
    )
    return tf.estimator.export.TensorServingInputReceiver(
        images, {"image_bytes": image_bytes_list}
    )


def resnet_model_fn(
    features,
    labels,
    mode,
    model_class,
    resnet_size,
    weight_decay,
    learning_rate_fn,
    momentum,
    data_format,
    resnet_version,
    loss_scale,
    loss_filter_fn=None,
    dtype=resnet_model.DEFAULT_DTYPE,
    fine_tune=False,
):
    """Shared functionality for different resnet model_fns.

    Initializes the ResnetModel representing the model layers
    and uses that model to build the necessary EstimatorSpecs for
    the `mode` in question. For training, this means building losses,
    the optimizer, and the train op that get passed into the EstimatorSpec.
    For evaluation and prediction, the EstimatorSpec is returned without
    a train op, but with the necessary parameters for the given mode.

    Args:
      features: tensor representing input images
      labels: tensor representing class labels for all input images
      mode: current estimator mode; should be one of
        `tf.estimator.ModeKeys.TRAIN`, `EVALUATE`, `PREDICT`
      model_class: a class representing a TensorFlow model that has a __call__
        function. We assume here that this is a subclass of ResnetModel.
      resnet_size: A single integer for the size of the ResNet model.
      weight_decay: weight decay loss rate used to regularize learned variables.
      learning_rate_fn: function that returns the current learning rate given
        the current global_step
      momentum: momentum term used for optimization
      data_format: Input format ('channels_last', 'channels_first', or None).
        If set to None, the format is dependent on whether a GPU is available.
      resnet_version: Integer representing which version of the ResNet network to
        use. See README for details. Valid values: [1, 2]
      loss_scale: The factor to scale the loss for numerical stability. A detailed
        summary is present in the arg parser help text.
      loss_filter_fn: function that takes a string variable name and returns
        True if the var should be included in loss calculation, and False
        otherwise. If None, batch_normalization variables will be excluded
        from the loss.
      dtype: the TensorFlow dtype to use for calculations.
      fine_tune: If True only train the dense layers(final layers).

    Returns:
      EstimatorSpec parameterized according to the input params and the
      current mode.
    """

    model = model_class(
        resnet_size, data_format, resnet_version=resnet_version, dtype=dtype
    )

    logits = model(features, mode == tf.estimator.ModeKeys.TRAIN)

    # This acts as a no-op if the logits are already in fp32 (provided logits are
    # not a SparseTensor). If dtype is is low precision, logits must be cast to
    # fp32 for numerical stability.
    logits = tf.cast(logits, tf.float32)

    predictions = {
        "classes": tf.argmax(logits, axis=1),
        "probabilities": tf.nn.softmax(logits, name="softmax_tensor"),
    }

    if mode == tf.estimator.ModeKeys.PREDICT:
        # Return the predictions and the specification for serving a SavedModel
        return tf.estimator.EstimatorSpec(
            mode=mode,
            predictions=predictions,
            export_outputs={
                "predict": tf.estimator.export.PredictOutput(predictions)},
        )

    # Calculate loss, which includes softmax cross entropy and L2
    # regularization.
    cross_entropy = tf.losses.sparse_softmax_cross_entropy(
        logits=logits, labels=labels)

    # Create a tensor named cross_entropy for logging purposes.
    tf.identity(cross_entropy, name="cross_entropy")

    # If no loss_filter_fn is passed, assume we want the default behavior,
    # which is that batch_normalization variables are excluded from loss.
    def exclude_batch_norm(name):
        return "batch_normalization" not in name

    loss_filter_fn = loss_filter_fn or exclude_batch_norm

    # Add weight decay to the loss.
    l2_loss = weight_decay * tf.add_n(
        # loss is computed using fp32 for numerical stability.
        [
            tf.nn.l2_loss(tf.cast(v, tf.float32))
            for v in tf.trainable_variables()
            if loss_filter_fn(v.name)
        ]
    )
    tf.summary.scalar("l2_loss", l2_loss)
    loss = cross_entropy + l2_loss

    return tf.estimator.EstimatorSpec(
        mode=mode,
        predictions=predictions,
        loss=loss,
        train_op=train_op,
        eval_metric_ops=metrics,
    )


def resnet_main(flags_obj, model_function,
                input_function, dataset_name, shape=None):
    """Shared main loop for ResNet Models.

    Args:
      flags_obj: An object containing parsed flags. See define_resnet_flags()
        for details.
      model_function: the function that instantiates the Model and builds the
        ops for train/eval. This will be passed directly into the estimator.
      input_function: the function that processes the dataset and returns a
        dataset that the estimator can train on. This will be wrapped with
        all the relevant flags for running and passed to estimator.
      dataset_name: the name of the dataset for training and evaluation. This is
        used for logging purpose.
      shape: list of ints representing the shape of the images used for training.
        This is only used if flags_obj.export_dir is passed.
    """

    print("RESNET MAIN")
    model_helpers.apply_clean(flags.FLAGS)

    # Ensures flag override logic is only executed if explicitly triggered.
    if flags_obj.tf_gpu_thread_mode:
        override_flags_and_set_envars_for_gpu_thread_pool(flags_obj)

    # Creates session config. allow_soft_placement = True, is required for
    # multi-GPU and is not harmful for other modes.
    session_config = tf.ConfigProto(allow_soft_placement=True)

    run_config = tf.estimator.RunConfig(
        session_config=session_config, save_checkpoints_secs=60 * 60 * 24
    )

    # Initializes model with all but the dense layer from pretrained ResNet.
    if flags_obj.pretrained_model_checkpoint_path is not None:
        warm_start_settings = tf.estimator.WarmStartSettings(
            flags_obj.pretrained_model_checkpoint_path,
            vars_to_warm_start="^(?!.*dense)",
        )
    else:
        warm_start_settings = None

    classifier = tf.estimator.Estimator(
        model_fn=model_function,
        model_dir=flags_obj.model_dir,
        config=run_config,
        warm_start_from=warm_start_settings,
        params={
            "resnet_size": int(flags_obj.resnet_size),
            "data_format": flags_obj.data_format,
            "batch_size": flags_obj.batch_size,
            "resnet_version": int(flags_obj.resnet_version),
            "loss_scale": flags_core.get_loss_scale(flags_obj),
            "dtype": flags_core.get_tf_dtype(flags_obj),
            "fine_tune": flags_obj.fine_tune,
        },
    )

    run_params = {
        "batch_size": flags_obj.batch_size,
        "dtype": flags_core.get_tf_dtype(flags_obj),
        "resnet_size": flags_obj.resnet_size,
        "resnet_version": flags_obj.resnet_version,
        "synthetic_data": flags_obj.use_synthetic_data,
        "train_epochs": flags_obj.train_epochs,
    }

    def input_fn_eval():
        return input_function(
            is_training=False,
            data_dir=flags_obj.data_dir,
            batch_size=distribution_utils.per_device_batch_size(
                flags_obj.batch_size, flags_core.get_num_gpus(flags_obj)
            ),
            num_epochs=1,
            dtype=flags_core.get_tf_dtype(flags_obj),
        )

    schedule, n_loops = [0], 1
    if flags_obj.export_dir is not None:
        # Exports a saved model for the given classifier.
        export_dtype = flags_core.get_tf_dtype(flags_obj)
        if flags_obj.image_bytes_as_serving_input:
            input_receiver_fn = functools.partial(
                image_bytes_serving_input_fn, shape, dtype=export_dtype
            )
        else:
            input_receiver_fn = export.build_tensor_serving_input_receiver_fn(
                shape, batch_size=flags_obj.batch_size, dtype=export_dtype
            )
        classifier.export_savedmodel(
            flags_obj.export_dir, input_receiver_fn, strip_default_attrs=True
        )


def define_resnet_flags(resnet_size_choices=None):
    """Add flags and validators for ResNet."""
    flags_core.define_base()
    flags_core.define_performance(
        num_parallel_calls=False,
        tf_gpu_thread_mode=True,
        datasets_num_private_threads=True,
        datasets_num_parallel_batches=True,
    )
    flags_core.define_image()
    flags_core.define_benchmark()
    flags.adopt_module_key_flags(flags_core)

    flags.DEFINE_enum(
        name="resnet_version",
        short_name="rv",
        default="1",
        enum_values=["1", "2"],
        help=flags_core.help_wrap(
            "Version of ResNet. (1 or 2) See README.md for details."
        ),
    )
    flags.DEFINE_bool(
        name="fine_tune",
        short_name="ft",
        default=False,
        help=flags_core.help_wrap(
            "If True do not train any parameters except for the final layer."
        ),
    )
    flags.DEFINE_string(
        name="pretrained_model_checkpoint_path",
        short_name="pmcp",
        default=None,
        help=flags_core.help_wrap(
            "If not None initialize all the network except the final layer with "
            "these values"
        ),
    )
    flags.DEFINE_boolean(
        name="eval_only",
        default=False,
        help=flags_core.help_wrap(
            "Skip training and only perform evaluation on " "the latest checkpoint."
        ),
    )
    flags.DEFINE_boolean(
        name="image_bytes_as_serving_input",
        default=False,
        help=flags_core.help_wrap(
            "If True exports savedmodel with serving signature that accepts "
            "JPEG image bytes instead of a fixed size [HxWxC] tensor that "
            "represents the image. The former is easier to use for serving at "
            "the expense of image resize/cropping being done as part of model "
            "inference. Note, this flag only applies to ImageNet and cannot "
            "be used for CIFAR."
        ),
    )

    choice_kwargs = dict(
        name="resnet_size",
        short_name="rs",
        default="50",
        help=flags_core.help_wrap("The size of the ResNet model to use."),
    )

    if resnet_size_choices is None:
        flags.DEFINE_string(**choice_kwargs)
    else:
        flags.DEFINE_enum(enum_values=resnet_size_choices, **choice_kwargs)
