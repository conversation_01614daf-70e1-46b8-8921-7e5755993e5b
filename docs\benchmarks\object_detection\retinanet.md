---
hide:
  - toc
---

# Object Detection using Retinanet

=== "MLCommons-Python"
    ## MLPerf Reference Implementation in Python
    
{{ mlperf_inference_implementation_readme (4, "retinanet", "reference") }}

=== "Nvidia"
    ## Nvidia MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "retinanet", "nvidia") }}

=== "Intel"
    ## Intel MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "retinanet", "intel") }}

=== "Qualcomm"
    ## Qualcomm AI100 MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "retinanet", "qualcomm") }}

=== "MLCommons-C++"
    ## MLPerf Modular Implementation in C++
    
{{ mlperf_inference_implementation_readme (4, "retinanet", "cpp") }}
