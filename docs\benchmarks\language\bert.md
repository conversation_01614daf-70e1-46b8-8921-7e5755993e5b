---
hide:
  - toc
---

# Question Answering using Bert-Large

=== "MLCommons-Python"
    ## MLPerf Reference Implementation in Python
    
{{ mlperf_inference_implementation_readme (4, "bert-99", "reference") }}

{{ mlperf_inference_implementation_readme (4, "bert-99.9", "reference") }}

=== "Nvidia"
    ## Nvidia MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "bert-99", "nvidia") }}

{{ mlperf_inference_implementation_readme (4, "bert-99.9", "nvidia") }}

=== "Intel"
    ## Intel MLPerf Implementation
    
{{ mlperf_inference_implementation_readme (4, "bert-99", "intel") }}

{{ mlperf_inference_implementation_readme (4, "bert-99.9", "intel") }}

=== "Qualcomm"
    ## Qualcomm AI100 MLPerf Implementation

{{ mlperf_inference_implementation_readme (4, "bert-99", "qualcomm") }}

{{ mlperf_inference_implementation_readme (4, "bert-99.9", "qualcomm") }}
