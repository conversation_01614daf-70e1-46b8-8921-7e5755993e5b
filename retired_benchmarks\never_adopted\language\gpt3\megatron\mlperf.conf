# The format of this config file is 'key = value'.
# The key has the format 'model.scenario.key'. Value is mostly int64_t.
# Model maybe '*' as wildcard. In that case the value applies to all models.
# All times are in milli seconds

# Set performance_sample_count for each model.
# User can optionally set this to higher values in user.conf.
resnet50.*.performance_sample_count_override = 1024
ssd-mobilenet.*.performance_sample_count_override = 256
retinanet.*.performance_sample_count_override = 64
bert.*.performance_sample_count_override = 10833
dlrm.*.performance_sample_count_override = 204800
rnnt.*.performance_sample_count_override = 2513
# set to 0 to let entire sample set to be performance sample
3d-unet.*.performance_sample_count_override = 0

# Set seeds. The seeds will be distributed two weeks before the submission.
# 0x8ad40a335b00b614
*.*.qsl_rng_seed = 10003631887983097364
# 0xee765861048ca2ba
*.*.sample_index_rng_seed = 17183018601990103738
# 0xa867c8b40e66b636
*.*.schedule_rng_seed = 12134888396634371638
# Set seeds for TEST_05. The seeds will be distributed two weeks before the submission.
#0xcb41409ba0dd1540
*.*.test05_qsl_rng_seed = 14646058500348515648
#0x10c102904c81c5a2
*.*.test05_sample_index_rng_seed = 1207248993894122914
#0xa4db284011bcd876
*.*.test05_schedule_rng_seed = 11879132697760422006


*.SingleStream.target_latency_percentile = 90
*.SingleStream.min_duration = 600000
*.SingleStream.min_query_count = 1024

*.MultiStream.target_latency_percentile = 99
*.MultiStream.samples_per_query = 8
*.MultiStream.min_duration = 600000
*.MultiStream.min_query_count = 270336
retinanet.MultiStream.target_latency = 528

# 3D-UNet uses equal issue mode
3d-unet.*.sample_concatenate_permutation = 1

*.Server.target_latency = 10
*.Server.target_latency_percentile = 99
*.Server.target_duration = 0
*.Server.min_duration = 600000
*.Server.min_query_count = 270336
resnet50.Server.target_latency = 15
retinanet.Server.target_latency = 100
bert.Server.target_latency = 130
dlrm.Server.target_latency = 30
rnnt.Server.target_latency = 1000

*.Offline.target_latency_percentile = 90
*.Offline.min_duration = 600000
# In Offline scenario, we always have one query. But LoadGen maps this to
# min_sample_count internally in Offline scenario, so set this to 24576 since
# the rule requires that Offline scenario run for at least 24576 samples.
*.Offline.min_query_count = 24576

# These fields should be defined and overridden by user.conf.
*.SingleStream.target_latency = 10
*.MultiStream.target_latency = 80
*.Server.target_qps = 1.0
*.Offline.target_qps = 1.0
