name: Test for MLPerf inference rgat submission generation using MLC script automation

on:
  pull_request:
    branches: [ "master", "dev" ]
    paths:
      - graph/R-GAT/**
      - loadgen/**
      - tools/submission/**
      - .github/workflows/test-rgat.yml
      - '!**.md'

env:
  PR_HEAD_REF: ${{ github.event.pull_request.head.ref }}

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ "3.11" ]
        backend: [ "pytorch" ]
        loadgen-flag: [ "" ]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python3 -m pip install mlc-scripts
    - name: Test R-GAT and end to end submission generation
      run: |
        mlcr run,mlperf,inference,generate-run-cmds,_submission,_short --quiet --submitter="<PERSON><PERSON><PERSON><PERSON>" --category=datacenter --hw_name=default --model=rgat --implementation=reference --backend=${{ matrix.backend }} --device=cpu --scenario=Offline --test_query_count=500 --adr.compiler.tags=gcc --adr.inference-src.tags=_branch.$PR_HEAD_REF,_repo.${{ github.event.pull_request.head.repo.html_url }} --adr.inference-src-loadgen.tags=_branch.$PR_HEAD_REF,_repo.${{ github.event.pull_request.head.repo.html_url }} --adr.inference-src-loadgen.version=custom --adr.loadgen.version=custom ${{ matrix.loadgen-flag }}
