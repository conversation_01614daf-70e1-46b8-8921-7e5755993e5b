# vLLM Backend Dockerfile - Install from source
FROM nvidia/cuda:12.6.0-devel-ubuntu22.04

# Set environment variables to prevent interactive prompts during build
ENV DEBIAN_FRONTEND=noninteractive
# ENV PIP_BREAK_SYSTEM_PACKAGES=1
ENV MLPERF_BACKEND=vllm
# Set CUDA architectures for vLLM build (adjust based on your GPU)
ENV TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6;8.9;9.0"

# Install basic dependencies and build tools
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3-pip \
    python3-dev \
    git \
    curl \
    ca-certificates \
    build-essential \
    cmake \
    ninja-build \
    pybind11-dev \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip to latest version to avoid dependency conflicts
RUN python3 -m pip install --upgrade pip setuptools wheel

# Install UV package manager system-wide
RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
    mv /root/.local/bin/uv /usr/local/bin/uv && \
    mv /root/.local/bin/uvx /usr/local/bin/uvx && \
    chmod 755 /usr/local/bin/uv /usr/local/bin/uvx && \
    uv --version

# Set up a working directory for vLLM installation
WORKDIR /vllm-install

# Clone the vLLM repository (main branch)
RUN git clone https://github.com/vllm-project/vllm.git .

# Install PyTorch with CUDA support
RUN pip3 install --no-cache-dir torch==2.7.0 torchvision==0.22.0 torchaudio==2.7.0 --index-url https://download.pytorch.org/whl/cu126

# Install vLLM from source
# This will install the latest version from the cloned main branch
# RUN pip3 install --no-cache-dir -e .
RUN pip3 install vllm==0.9.0

# Install additional dependencies needed for MLPerf evaluation
RUN pip3 install --no-cache-dir \
    pandas \
    numpy \
    tqdm \
    huggingface-hub[hf_transfer] \
    datasets \
    accelerate

# Set cache directory environment variables
# These will be overridden at runtime if needed
ENV HF_HOME=/raid/data/\$USER/.cache
ENV HF_HUB_CACHE=/raid/data/\$USER/.cache
ENV HUGGINGFACE_HUB_CACHE=/raid/data/\$USER/.cache

# Set working directory to /work for mounted workspace
WORKDIR /work

# Add /work to PATH so setup.sh can be run without ./
ENV PATH="/work:${PATH}"

# Expose the default vLLM API port
EXPOSE 8000

# Set default command
CMD ["/bin/bash"]