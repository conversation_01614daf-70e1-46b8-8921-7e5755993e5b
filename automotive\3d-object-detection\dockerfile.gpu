ARG FROM_IMAGE_NAME=pytorch/pytorch:2.2.2-cuda11.8-cudnn8-devel
FROM ${FROM_IMAGE_NAME}

ENV DEBIAN_FRONTEND=noninteractive

# apt dependencies
RUN apt-get update
RUN apt-get install -y ffmpeg libsm6 libxext6 git

# install LDM
COPY . /diffusion
RUN cd /diffusion && \
    pip install --no-cache-dir -r requirements.txt

# install loadgen
RUN cd /tmp && \
    git clone --recursive https://github.com/mlcommons/inference && \
    cd inference/loadgen && \
    pip install pybind11 && \
    CFLAGS="-std=c++14" python setup.py install && \
    rm -rf mlperf

RUN pip install tqdm==4.65.0
RUN pip install numba==0.60.0
RUN pip install opencv-python==*********
RUN pip install open3d==0.19.0
RUN pip install scikit-image==0.25.0
RUN pip install ninja==1.11.1
RUN pip install shapely==2.0.6
RUN pip install tensorboard==2.18.0
RUN pip install onnxruntime==1.20.1